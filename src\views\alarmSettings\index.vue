<template>
  <cm-container>
    <div class="alarm-settings">
      <div class="alarm-settings-search">
        <el-input style="width: 240px" v-model="searchParams.userName"
          placeholder="请输入申请人" />
        <el-input style="width: 240px" v-model="searchParams.userCode"
          placeholder="请输入工号" />
        <el-input
          style="width: 240px"
          v-model="searchParams.deptName"
          placeholder="请输入所属部门" />
        <div class="alarm-settings-search-button">
          <el-button type="primary" @click="onSearch"> 搜索 </el-button>
          <el-button @click="onClear">清空</el-button>
        </div>
      </div>
      <div class="alarm-settings-operations">
        <el-button type="primary" size="small"
          @click="onBatchBysj('bdsj')">批量设置毕业设计</el-button>
        <el-button type="primary" size="small"
          @click="onBatchBysj('bdsjAll')">设置全部毕业设计</el-button>
        <el-button type="primary" size="small"
          @click="onBatchDgsx('dgsx')">批量设置顶岗实习</el-button>
        <el-button type="primary" size="small"
          @click="onBatchDgsx('dgsxAll')">设置全部顶岗实习</el-button>
        <el-button type="primary" size="small"
          @click="onBatchYjsj('yjsj')">批量设置预警时间</el-button>
        <el-button type="primary" size="small"
          @click="onBatchYjsj('yjsjAll')">设置全部预警时间</el-button>
      </div>
      <div class="alarm-settings-table" ref="tableRef">
        <el-table :data="tableData" style="width: 100%"
          :height="tableHeight">
          <el-table-column type="selection" width="55"
            align="center" />
          <el-table-column prop="grade" label="年级" />
          <el-table-column prop="yx" label="院系" />
          <el-table-column prop="zy" label="专业" />
          <el-table-column prop="kcyq" label="课程要求" />
          <el-table-column prop="bdsj" label="毕业设计" />
          <el-table-column prop="dgsx" label="顶岗实习" />
          <el-table-column prop="yjsj" label="预警时间" />
          <el-table-column label="操作" width="150" align="center">
            <template #default="scope">
              <el-button type="primary" link
                v-perms="'alarmSettings:setting'"
                @click="onJumpSetting(scope.row)">设置课程</el-button>
              <el-button type="primary" link
                @click="onClearSettings(scope.row)">清空</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="cm-pagination-right">
          <el-pagination v-model:current-page="pageParams.current"
            v-model:page-size="pageParams.size" background
            layout="total, sizes, prev, pager, next, jumper"
            :total="pageParams.total" @change="onLoad" />
        </div>
      </div>
    </div>
    <cm-dialog v-model="dialogBysjVisible" title="设置" width="500px"
      :close-on-click-modal="false" :close-on-press-escape="false"
      class="cm-dialog">
      <template #default>
        <div class="alarm-settings-dialog-content">
          <el-form
            ref="ruleFormBysjRef"
            :model="ruleForm"
            :rules="rules"
            label-width="auto">
            <el-form-item label="毕业设计成绩（大于等于）" prop="bysj">
              <el-input-number v-model="ruleForm.bysj"
                :min="0"
                class="inp-text-left"
                :controls="false"
                placeholder="请输入毕业设计成绩（大于等于）" />
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #footer>
        <el-button @click="dialogBysjVisible = false">取消</el-button>
        <el-button type="primary" @click="onConfirmBysj"
          :disabled="!ruleForm.bysj"
          :loading="approvalBysjLoading">
          确 定
        </el-button>
      </template>
    </cm-dialog>

    <cm-dialog v-model="dialogDgsxVisible" title="设置" width="500px"
      :close-on-click-modal="false" :close-on-press-escape="false"
      class="cm-dialog">
      <template #default>
        <div class="alarm-settings-dialog-content">
          <el-form
            ref="ruleFormDgsxRef"
            :model="ruleForm"
            :rules="rules"
            label-width="auto">
            <el-form-item label="顶岗实习成绩（大于等于）" prop="dgsx">
              <el-input-number v-model="ruleForm.dgsx"
                :min="0"
                class="inp-text-left"
                :controls="false"
                placeholder="请输入顶岗实习成绩（大于等于）" />
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #footer>
        <el-button @click="dialogDgsxVisible = false">取消</el-button>
        <el-button type="primary" @click="onConfirmDgsx"
          :disabled="!ruleForm.dgsx"
          :loading="approvalDgsxLoading">
          确 定
        </el-button>
      </template>
    </cm-dialog>

    <cm-dialog v-model="dialogYjsjVisible" title="设置" width="800px"
      :close-on-click-modal="false" :close-on-press-escape="false"
      @close="calendarVisible = false" class="cm-dialog">
      <template #default>
        <div class="alarm-settings-dialog-content">
          <div class="alarm-date"
            v-if="settingType === 'yjsj' || settingType === 'yjsjAll'">
            <div class="alarm-date-header">
              <span class="alarm-date-header-title">预警时间</span>
              <el-popover placement="bottom" :width="800"
                :visible="calendarVisible"
                trigger="click">
                <template #reference>
                  <el-button type="primary"
                    @click="showCalendar">添加日期</el-button>
                </template>
                <CalendarPanel @close="calendarVisible = false"
                  v-model="ruleForm.bdsj" @confirm="onConfirmDate" />
              </el-popover>
            </div>
            <div class="alarm-date-table">
              <el-table :data="alarmListDate" style="width: 100%"
                height="300">
                <el-table-column prop="start" label="开始日期" />
                <el-table-column prop="end" label="结束日期" />
                <el-table-column prop="durationText" label="时长" />
                <el-table-column label="操作" width="80" align="center">
                  <template #default="scope">
                    <el-button type="primary" link
                      @click="onDeleteAlarmListDate(scope.row.$index)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </template>
      <template #footer>
        <el-button @click="dialogYjsjVisible = false">取消</el-button>
        <el-button type="primary" @click="onConfirmYjsjAlarmListDate"
          :disabled="alarmListDate.length === 0"
          :loading="approvalYjsjLoading">
          确 定
        </el-button>
      </template>
    </cm-dialog>
  </cm-container>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import CmContainer from '@/components/cm-container/main.vue'
import { useResizeObserver } from '@/hooks/useResizeObserver'
import CmDialog from '@/components/cm-dialog/index.vue'
import CalendarPanel from './components/CalendarPanel.vue'

const dialogVisible = ref(false)
const ruleForm = reactive({})
const alarmListDate = ref([])
const settingType = ref('')
const rules = reactive({
  bdsj: [
    { required: true, message: '请输入毕业设计成绩', trigger: 'blur' },
  ],
  dgsx: [
    { required: true, message: '请输入顶岗实习成绩', trigger: 'blur' },
  ]
})

/**
 * 表格引用
 * @type {Ref<HTMLElement>}
 */
const tableRef = useTemplateRef('tableRef')

/**
 * 表格高度
 * @type {Ref<number>}
 */
const tableHeight = ref(0)
const tableData = ref([
  {
    grade: '2022',
    yx: '计算机科学与',
    zy: '软件工程',
    kcyq: '123456',
    bdsj: '123456',
    dgsx: '123456',
    yjsj: '123456',
  },
])

/**
 * 分页参数
 * @type {Ref<Object>}
 * @property {number} total 总条数
 * @property {number} current 当前页
 * @property {number} size 每页条数
 */
const pageParams = ref({
  total: 0,
  current: 1,
  size: 10,
})

/**
 * 搜索参数
 * @type {Ref<Object>}
 * @property {string} userName 申请人
 * @property {string} userCode 工号
 * @property {string} deptName 所属部门
 */
const searchParams = ref({
  userName: '',
  userCode: '',
  deptName: '',
})

/**
 * 初始化
 */
onMounted(() => {
  useResizeObserver(tableRef, entries => {
    const entry = entries[0]
    const { height: _h } = entry.contentRect
    tableHeight.value = _h - 54
  })
})

/**
 * 搜索
 */
const onSearch = () => {
  console.log(searchParams.value)
}

/**
 * 清空搜索
 */
const onClear = () => {
  searchParams.value = {}
}

/**
 * 加载数据
 */
const onLoad = () => {
  console.log(pageParams.value)
}

const router = useRouter()

const onJumpSetting = (row) => {
  router.push({
    path: '/alarmSettings/setting',
    query: {
      id: row.id,
    },
  })
}






//毕业设计设置
const ruleFormBysjRef = useTemplateRef('ruleFormBysjRef')
const dialogBysjVisible = ref(false)
const approvalBysjLoading = ref(false)
const onBatchBysj = (type) => {
  settingType.value = type
  dialogBysjVisible.value = true
}
const onConfirmBysj = () => {
  ruleFormBysjRef.value.validate((valid) => {
    if (valid) {
      approvalBysjLoading.value = true
    }
  })
}

//顶岗实习设置
const ruleFormDgsxRef = useTemplateRef('ruleFormDgsxRef')
const dialogDgsxVisible = ref(false)
const approvalDgsxLoading = ref(false)
const onBatchDgsx = (type) => {
  settingType.value = type
  dialogDgsxVisible.value = true
}
const onConfirmDgsx = () => {
  ruleFormDgsxRef.value.validate((valid) => {
    if (valid) {
      approvalDgsxLoading.value = true
    }
  })
}


//预警时间设置
const dialogYjsjVisible = ref(false)
const approvalYjsjLoading = ref(false)
const calendarVisible = ref(false)
const onBatchYjsj = (type) => {
  settingType.value = type
  dialogYjsjVisible.value = true
}
const showCalendar = () => {
  calendarVisible.value = !calendarVisible.value
}

const onConfirmDate = (data) => {
  alarmListDate.value.push(data)
}
/**
 * 清空设置
 * @param {Object} row 行数据
 */
const onDeleteAlarmListDate = index => {
  ElMessageBox.confirm('确定删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      alarmListDate.value.splice(index, 1)
    })
    .catch(() => { })
}
const onConfirmYjsjAlarmListDate = () => {
  approvalYjsjLoading.value = true
  setTimeout(() => {
    approvalYjsjLoading.value = false
    dialogYjsjVisible.value = false
  }, 3000)
}
</script>

<style lang="scss" scoped>
.alarm-settings {
  width: 100%;
  height: 100%;
  background-color: #fff;
  padding: 0 10px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 12px;

  .alarm-settings-search {
    margin-top: 12px;
    display: flex;
    align-items: center;
    gap: 10px;

    .alarm-settings-search-button {
      display: flex;
      align-items: center;
    }
  }

  .alarm-settings-operations {
    display: flex;
    align-items: center;
  }

  .alarm-settings-table {
    flex: 1;
  }
}

.cm-dialog {
  .alarm-settings-dialog-content {
    padding: 20px;
  }

  .alarm-date {
    background: #F9FAFF;
    border: 1px solid #FFFFFF;
    backdrop-filter: blur(10px);
    padding: 10px;
    box-sizing: border-box;

    .alarm-date-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .alarm-date-header-title {
        font-family: AppleSystemUIFont;
        font-size: 16px;
        color: #333333;
      }

    }

    .alarm-date-table {
      margin-top: 10px;
    }
  }
}
</style>
